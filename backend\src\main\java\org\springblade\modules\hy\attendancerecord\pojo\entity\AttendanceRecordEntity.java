/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.attendancerecord.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 参会签到记录表 实体类
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@TableName("hy_attendance_record")
@Schema(description = "AttendanceRecord对象")
@EqualsAndHashCode(callSuper = true)
public class AttendanceRecordEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户ID
	 */
	@Schema(description = "用户ID")
	private Long userId;
	/**
	 * 议程ID
	 */
	@Schema(description = "议程ID")
	private Long hyAgendaId;
	/**
	 * 签到时间
	 */
	@Schema(description = "签到时间")
	private LocalDateTime checkinTime;
	/**
	 * 签到状态
	 */
	@Schema(description = "签到状态")
	private String statusText;

	private String checkinType;

	private String checkinCode;

	private String deviceInfo;

	private String location;

}
