<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    
    <title>会务系统 - 移动端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            max-width: 350px;
            width: 100%;
        }
        
        .logo {
            font-size: 48px;
            color: #4682B4;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
        }
        
        .access-btn {
            background: #4682B4;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 20px;
        }
        
        .access-btn:hover {
            background: #3a6fa5;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
        }
        
        .access-btn:active {
            transform: translateY(0);
        }
        
        .info {
            color: #666;
            font-size: 14px;
            line-height: 1.6;
            margin-top: 20px;
        }
        
        .features {
            text-align: left;
            margin-top: 25px;
        }
        
        .features h3 {
            color: #4682B4;
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .features ul {
            list-style: none;
            padding: 0;
        }
        
        .features li {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .features li::before {
            content: "✓";
            color: #4682B4;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📱</div>
        <h1>会务系统移动端</h1>
        <p class="subtitle">专为移动设备优化的会务体验</p>
        
        <button class="access-btn" onclick="accessMobile()">
            进入移动端页面
        </button>
        
        <div class="features">
            <h3>功能特色</h3>
            <ul>
                <li>全屏沉浸式体验</li>
                <li>无左侧菜单和顶部标签栏</li>
                <li>专为触摸操作优化</li>
                <li>完整的会务功能</li>
                <li>流畅的页面切换</li>
            </ul>
        </div>
        
        <div class="info">
            <p>此页面专为移动设备设计，提供简洁高效的会务管理体验。</p>
        </div>
    </div>

    <script>
        function accessMobile() {
            // 跳转到移动端页面
            window.location.href = '/mobile';
        }
        
        // 检测设备类型
        function isMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }
        
        // 如果是移动设备，可以自动跳转
        if (isMobile()) {
            document.querySelector('.access-btn').innerHTML = '📱 进入移动端页面';
        }
        
        // 添加触摸反馈
        document.querySelector('.access-btn').addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
        });
        
        document.querySelector('.access-btn').addEventListener('touchend', function() {
            this.style.transform = 'scale(1)';
        });
    </script>
</body>
</html>
